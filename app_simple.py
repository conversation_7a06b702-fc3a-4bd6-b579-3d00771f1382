#!/usr/bin/env python3
"""
NeuroScan - Version simplifiée avec système d'évolution tumorale
"""

from flask import Flask, render_template, request, jsonify, Response
import sqlite3
import json
import os
import base64
import time
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
import random

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Configuration de la base de données
DATABASE_PATH = 'neuroscan_analytics.db'

# Classes de tumeurs
TUMOR_CLASSES = ['Normal', 'Gliome', 'Méningiome', 'Tumeur pituitaire']

def init_database():
    """Initialiser la base de données avec les tables nécessaires"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Table des patients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT UNIQUE NOT NULL,
            nom TEXT,
            prenom TEXT,
            date_naissance DATE,
            sexe TEXT CHECK(sexe IN ('M', 'F', 'Autre')),
            medecin_referent TEXT,
            notes_medicales TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des examens IRM
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS examens_irm (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date DATETIME NOT NULL,
            filename TEXT,
            image_metadata TEXT,
            predicted_class INTEGER,
            predicted_label TEXT,
            confidence REAL,
            probabilities TEXT,
            description TEXT,
            recommendations TEXT,
            processing_time REAL,
            user_session TEXT,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id)
        )
    ''')

    # Table d'analyse comparative d'évolution
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS evolution_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_id_previous INTEGER,
            exam_id_current INTEGER,
            time_interval_days INTEGER,
            evolution_type TEXT CHECK(evolution_type IN ('stable', 'progression', 'regression', 'changement_type')),
            confidence_change REAL,
            probability_changes TEXT,
            volume_change_estimate TEXT,
            alert_level TEXT CHECK(alert_level IN ('none', 'low', 'medium', 'high', 'critical')),
            medical_interpretation TEXT,
            recommendations TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (exam_id_previous) REFERENCES examens_irm(id),
            FOREIGN KEY (exam_id_current) REFERENCES examens_irm(id)
        )
    ''')

    # Index pour optimiser les requêtes temporelles
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_exam_date ON examens_irm(patient_id, exam_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_patient ON evolution_analysis(patient_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_alert ON evolution_analysis(alert_level)')

    conn.commit()
    conn.close()

def create_or_get_patient(patient_id, nom=None, prenom=None, date_naissance=None, sexe=None, medecin_referent=None):
    """Créer un nouveau patient ou récupérer un patient existant"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # Vérifier si le patient existe déjà
        cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
        existing_patient = cursor.fetchone()
        
        if existing_patient:
            # Mettre à jour la date de dernière modification
            cursor.execute('''
                UPDATE patients SET updated_at = CURRENT_TIMESTAMP WHERE patient_id = ?
            ''', (patient_id,))
            conn.commit()
            conn.close()
            return existing_patient[0]  # Retourner l'ID
        else:
            # Créer un nouveau patient
            cursor.execute('''
                INSERT INTO patients (patient_id, nom, prenom, date_naissance, sexe, medecin_referent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (patient_id, nom, prenom, date_naissance, sexe, medecin_referent))
            
            patient_db_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return patient_db_id
            
    except Exception as e:
        print(f"Erreur lors de la création/récupération du patient: {e}")
        return None

def simulate_tumor_prediction(filename):
    """Simuler une prédiction de tumeur intelligente"""
    # Prédictions basées sur le nom du fichier ou aléatoires
    predictions = [
        {'class': 0, 'label': 'Normal', 'confidence': 0.85, 'probs': {'Normal': 0.85, 'Gliome': 0.08, 'Méningiome': 0.05, 'Tumeur pituitaire': 0.02}},
        {'class': 1, 'label': 'Gliome', 'confidence': 0.78, 'probs': {'Normal': 0.12, 'Gliome': 0.78, 'Méningiome': 0.07, 'Tumeur pituitaire': 0.03}},
        {'class': 2, 'label': 'Méningiome', 'confidence': 0.82, 'probs': {'Normal': 0.08, 'Gliome': 0.06, 'Méningiome': 0.82, 'Tumeur pituitaire': 0.04}},
        {'class': 3, 'label': 'Tumeur pituitaire', 'confidence': 0.75, 'probs': {'Normal': 0.15, 'Gliome': 0.05, 'Méningiome': 0.05, 'Tumeur pituitaire': 0.75}}
    ]
    
    # Logique de sélection basée sur le nom
    filename_lower = filename.lower()
    if 'normal' in filename_lower:
        pred = predictions[0]
    elif 'gliome' in filename_lower or 'glio' in filename_lower:
        pred = predictions[1]
    elif 'menin' in filename_lower:
        pred = predictions[2]
    elif 'pitui' in filename_lower:
        pred = predictions[3]
    else:
        pred = random.choice(predictions)
    
    # Ajouter variation réaliste
    confidence_variation = random.uniform(-0.1, 0.1)
    pred['confidence'] = max(0.5, min(0.95, pred['confidence'] + confidence_variation))
    
    # Générer description et recommandations
    descriptions = {
        'Normal': f"Structures cérébrales normales détectées avec {pred['confidence']:.1%} de confiance.",
        'Gliome': f"Gliome détecté avec {pred['confidence']:.1%} de confiance. Tumeur gliale nécessitant évaluation.",
        'Méningiome': f"Méningiome identifié avec {pred['confidence']:.1%} de confiance. Tumeur des méninges.",
        'Tumeur pituitaire': f"Tumeur pituitaire détectée avec {pred['confidence']:.1%} de confiance."
    }
    
    recommendations = {
        'Normal': ["Suivi de routine", "Contrôles périodiques"],
        'Gliome': ["Consultation neurochirurgicale urgente", "IRM avec contraste", "Évaluation multidisciplinaire"],
        'Méningiome': ["Consultation neurochirurgicale", "Surveillance radiologique", "Évaluation symptômes"],
        'Tumeur pituitaire': ["Consultation endocrinologique", "Bilan hormonal", "IRM hypophysaire"]
    }
    
    return {
        'predicted_class': pred['class'],
        'predicted_label': pred['label'],
        'confidence': pred['confidence'],
        'probabilities': pred['probs'],
        'description': descriptions[pred['label']],
        'recommendations': recommendations[pred['label']]
    }

def save_exam_to_db(patient_id, results, filename, processing_time, exam_date=None, 
                   image_metadata=None, session_id=None, ip_address=None):
    """Sauvegarder un examen IRM dans la nouvelle structure"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Utiliser la date actuelle si non spécifiée
        if exam_date is None:
            exam_date = datetime.now()

        # Convertir les données en JSON
        probabilities_json = json.dumps(results['probabilities'])
        recommendations_json = json.dumps(results.get('recommendations', []))
        metadata_json = json.dumps(image_metadata) if image_metadata else None

        cursor.execute('''
            INSERT INTO examens_irm
            (patient_id, exam_date, filename, image_metadata, predicted_class, predicted_label, 
             confidence, probabilities, description, recommendations, processing_time, 
             user_session, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            exam_date,
            filename,
            metadata_json,
            results['predicted_class'],
            results['predicted_label'],
            results['confidence'],
            probabilities_json,
            results.get('description', ''),
            recommendations_json,
            processing_time,
            session_id,
            ip_address
        ))

        exam_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return exam_id
        
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'examen: {e}")
        return None

def analyze_tumor_evolution(patient_id, current_exam_id):
    """Analyser l'évolution tumorale en comparant avec les examens précédents"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # Récupérer l'examen actuel
        cursor.execute('SELECT * FROM examens_irm WHERE id = ?', (current_exam_id,))
        current_exam = cursor.fetchone()
        
        if not current_exam:
            return None
            
        # Récupérer l'examen précédent le plus récent
        cursor.execute('''
            SELECT * FROM examens_irm 
            WHERE patient_id = ? AND exam_date < ? 
            ORDER BY exam_date DESC 
            LIMIT 1
        ''', (patient_id, current_exam[2]))  # exam_date est à l'index 2
        
        previous_exam = cursor.fetchone()
        
        if not previous_exam:
            # Premier examen pour ce patient
            conn.close()
            return {
                'is_first_exam': True,
                'message': 'Premier examen pour ce patient - pas de comparaison possible'
            }
        
        # Calculer l'évolution
        evolution_data = calculate_evolution_metrics(previous_exam, current_exam)
        
        # Sauvegarder l'analyse d'évolution
        if evolution_data:
            save_evolution_analysis(cursor, patient_id, previous_exam[0], current_exam_id, evolution_data)
        
        conn.commit()
        conn.close()
        
        return evolution_data
        
    except Exception as e:
        print(f"Erreur lors de l'analyse d'évolution: {e}")
        return None

def calculate_evolution_metrics(previous_exam, current_exam):
    """Calculer les métriques d'évolution entre deux examens"""
    try:
        # Parser les probabilités JSON
        prev_probs = json.loads(previous_exam[8])  # probabilities à l'index 8
        curr_probs = json.loads(current_exam[8])
        
        # Calculer l'intervalle de temps
        prev_date = datetime.fromisoformat(previous_exam[2])
        curr_date = datetime.fromisoformat(current_exam[2])
        time_interval = (curr_date - prev_date).days
        
        # Analyser les changements de probabilités
        prob_changes = {}
        max_change = 0
        max_change_type = None
        
        for tumor_type in prev_probs.keys():
            change = curr_probs[tumor_type] - prev_probs[tumor_type]
            prob_changes[tumor_type] = {
                'previous': prev_probs[tumor_type],
                'current': curr_probs[tumor_type],
                'change': change,
                'change_percent': (change / prev_probs[tumor_type] * 100) if prev_probs[tumor_type] > 0 else 0
            }
            
            if abs(change) > abs(max_change):
                max_change = change
                max_change_type = tumor_type
        
        # Déterminer le type d'évolution
        prev_label = previous_exam[6]  # predicted_label
        curr_label = current_exam[6]
        
        if prev_label != curr_label:
            evolution_type = 'changement_type'
            alert_level = 'critical' if 'Gliome' in curr_label else 'high'
        else:
            current_prob_change = prob_changes[curr_label]['change']
            if abs(current_prob_change) < 0.05:
                evolution_type = 'stable'
                alert_level = 'none'
            elif current_prob_change > 0:
                evolution_type = 'progression'
                alert_level = 'medium' if current_prob_change > 0.2 else 'low'
            else:
                evolution_type = 'regression'
                alert_level = 'low'
        
        # Générer interprétation médicale
        interpretation = f"Évolution {evolution_type} détectée sur {time_interval} jours"
        if prev_label != curr_label:
            interpretation += f" - Changement: {prev_label} → {curr_label}"
        
        return {
            'is_first_exam': False,
            'time_interval_days': time_interval,
            'evolution_type': evolution_type,
            'confidence_change': current_exam[7] - previous_exam[7],
            'probability_changes': prob_changes,
            'alert_level': alert_level,
            'medical_interpretation': interpretation,
            'previous_diagnosis': prev_label,
            'current_diagnosis': curr_label,
            'max_change_type': max_change_type,
            'max_change_value': max_change
        }
        
    except Exception as e:
        print(f"Erreur lors du calcul des métriques: {e}")
        return None

def save_evolution_analysis(cursor, patient_id, prev_exam_id, curr_exam_id, evolution_data):
    """Sauvegarder l'analyse d'évolution dans la base de données"""
    try:
        cursor.execute('''
            INSERT INTO evolution_analysis
            (patient_id, exam_id_previous, exam_id_current, time_interval_days,
             evolution_type, confidence_change, probability_changes, alert_level,
             medical_interpretation, recommendations)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            prev_exam_id,
            curr_exam_id,
            evolution_data['time_interval_days'],
            evolution_data['evolution_type'],
            evolution_data['confidence_change'],
            json.dumps(evolution_data['probability_changes']),
            evolution_data['alert_level'],
            evolution_data['medical_interpretation'],
            json.dumps([])  # Recommandations à implémenter
        ))
        
        return True
        
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'analyse d'évolution: {e}")
        return False

# Initialiser la base de données au démarrage
init_database()

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/evolution-dashboard')
def evolution_dashboard():
    """Page du tableau de bord d'évolution tumorale"""
    return render_template('evolution_dashboard.html')
